
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>再次提取的内容 - 20250811072524</title>
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提取的Markdown内容</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css">
    <script defer="" src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
img {
    max-width: 100%;
    max-height: 100vh;
    height: auto;
    width: auto;
    display: block;
    margin: 0 auto;
}
body {
    margin: 0;
    padding: 20px;
    font-family: Arial, sans-serif;
    padding-bottom: 270px; /* 为浮动按钮增加底部内边距，防止遮挡内容 (220px bottom + 30px height + 20px buffer) */
}
.immersive-translate-target-inner {
    color: blue;
}
td, th {
    text-align: center;
    vertical-align: middle;
}
/* 浮动按钮样式现在由内联样式提供，此处无需额外CSS，除非需要 hover 等伪类效果 */
/* 
#plugin-trigger-button:hover {
    background-color: #0056b3; // 示例：如果需要 hover 效果可以这样添加
}
*/
    </style>
<style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-dialog {
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  display: flex;
  width: 300px;
  flex-direction: column;
  align-items: center;
  font-size: 15px;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  height: fit-content;
  border-radius: 20px;
  background-color: #fff;
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 25% auto !important;
  }
}

@media screen and (max-width: 480px) {
  .immersive-translate-modal-content {
    width: 80vw !important;
    margin: 20vh auto !important;
    padding: 20px 12px 12px !important;
  }

  .immersive-translate-modal-title {
    font-size: 14px !important;
  }

  .immersive-translate-modal-body {
    font-size: 13px !important;
    max-height: 60vh !important;
  }

  .immersive-translate-btn {
    font-size: 13px !important;
    padding: 8px 16px !important;
    margin: 0 4px !important;
  }

  .immersive-translate-modal-footer {
    gap: 6px !important;
    margin-top: 16px !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-link-btn {
  background-color: transparent;
  color: #ea4c89;
  border: none;
  cursor: pointer;
  height: 30px;
  line-height: 30px;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style>
    <style> 
        /* 确保再次下载的文件中 body padding 正常，且如果按钮意外残留则隐藏 */
        body { padding-bottom: 20px !important; } 
        #plugin-trigger-button { display: none !important; } /* ID 已更新 */
    </style>
</head>
<body>
    
    <div class="wrap center full svelte-au1olv hide" style="position: absolute; padding: 0px;"> <div class="svelte-1ed2p3z"> <span class="md svelte-8tpqd2 prose"><p>党委【2017】010 号  </p>
<p>(四) 热心群众工作，清正廉洁，在群众中有一定威信。<br>第十四条 工会经费审查委员会委员名额一般为3—7人，设主任委员1人，候选人应符合<br>以下条件：(一) 熟悉工会财务和审计业务。(二) 熟悉国家财政政策及相关规定。(三) 办事公道，坚持原则。<br>第十五条 工会女职工委员会委员候选人应符合以下条件：(一) 应由女性工会主席或副主席担任，若企业无女性工会主席或副主席的，由符合相应条件的女性工会委员担任。(二) 热心女职工工作，依法维护女职工的合法权益，清正廉洁，在女职工中有一定威信。(三) 有较强的参政议事能力。  </p>
<h1>第三章 职工代表大会与工会委员会</h1>
<p>第十六条 职工代表大会是工会工作的重点，工会委员会是职工代表大会的工作机构，支持职工代表大会闭会后的日常工作，各分工会负责本部门的民主管理日常工作。  </p>
<p>第十七条 职工代表大会工作机构的任务是：  </p>
<p>（一） 组织职工选举职工代表。<br>（二） 主持职工代表大会的筹备工作和会议期间的组织工作。<br>（三） 提出职代会议题的建议，报企业党委批准。<br>（四） 组织职代会专门工作小组开展工作，进行调查研究，向职代会提出建议，检查监督大会决议的执行情况。<br>（五） 征集、整理提案交职代会提案审查委员会审理。<br>（六） 工会委员由职工代表大会民主选举产生。具体成员及分工见表 1。  </p>
<p>表1 工会委员介绍表  </p>
<table class="table table-striped table-bordered"><tbody><tr><td style="font-weight: bold; font-size: 20px;">职位</td><td style="font-weight: bold; font-size: 20px;">人数</td><td style="font-weight: bold; font-size: 20px;">工作职责</td></tr><tr><td style="font-weight: normal;">工会主席</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">全面负责工会工作</td></tr><tr><td style="font-weight: normal;">工会副主席</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">主持工会日常工作</td></tr><tr><td style="font-weight: normal;">女工委员</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">负责女工委全面工作</td></tr><tr><td style="font-weight: normal;">经审委员</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">负责工会经费使用监督审查工作</td></tr><tr><td style="font-weight: normal;">组织委员</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">负责工会组织工作</td></tr><tr><td style="font-weight: normal;">文体委员</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">负责工会文体活动等工作</td></tr><tr><td style="font-weight: normal;">宣传委员</td><td style="font-weight: normal;">1</td><td style="font-weight: normal;">负责工会宣传工作</td></tr></tbody></table>  

<h1>第四章 工作职责</h1>
<p>第十八条 工会在公司党支部的领导及公司行政的支持下，独立自主地开展工作，其职责包括：  </p>
</span></div></div>

    <!-- 新的浮动按钮，使用用户提供的HTML和内联样式 -->
    

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // KaTeX 渲染 (已存在)
            if (typeof renderMathInElement === "function") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\[", right: "\]", display: true},
                        {left: "\(", right: "\)", display: false}
                    ]
                });
            }

            // 为新的浮动按钮 (#plugin-trigger-button) 添加点击事件处理
            const triggerButton = document.getElementById('plugin-trigger-button'); // ID 已更新
            if (triggerButton) {
                triggerButton.addEventListener('click', function() {
                    console.log("Plugin trigger button clicked, re-extracting current HTML content.");

                    // 1. 克隆当前页面的 body 节点
                    const bodyClone = document.body.cloneNode(true);

                    // 2. 从克隆的 body 中移除浮动按钮本身
                    const buttonInClone = bodyClone.querySelector('#plugin-trigger-button'); // ID 已更新
                    if (buttonInClone) {
                        buttonInClone.parentNode.removeChild(buttonInClone);
                    }
                    
                    let extractedBodyHtml = bodyClone.innerHTML;

                    // 3. 获取当前页面的 <head> 内容的 innerHTML
                    let headHtml = document.head.innerHTML;
                    
                    // 4. 构建新的完整HTML文档字符串
                    const newFullHtmlContentToDownload = `
<!DOCTYPE html>
<html lang="${document.documentElement.lang || 'zh-CN'}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>再次提取的内容 - ${new Date().toISOString().replace(/[T:.-]/g, '').substring(0,14)}</title>
    ${headHtml}
    <style> 
        /* 确保再次下载的文件中 body padding 正常，且如果按钮意外残留则隐藏 */
        body { padding-bottom: 20px !important; } 
        #plugin-trigger-button { display: none !important; } /* ID 已更新 */
    </style>
</head>
<body>
    ${extractedBodyHtml}
</body>
</html>
`;

                    // 5. 下载逻辑
                    const blob = new Blob([newFullHtmlContentToDownload], { type: "text/html;charset=utf-8" });
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    const fileName = `re_extracted_content_${timestamp}.html`;
                    const link = document.createElement("a");
                    link.href = URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    console.log("Content re-extracted and download initiated as " + fileName);
                });
            }
        });
    </script>



</body>
</html>
