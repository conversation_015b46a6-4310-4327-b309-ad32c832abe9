#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML文件合并工具
功能：将指定目录下的所有HTML文件合并成一个文件，并清除特定的浮动按钮代码
"""

import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from datetime import datetime
import glob
from pathlib import Path


class HTMLMerger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("HTML文件合并工具")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # 设置样式
        self.setup_ui()

        # 要清除的代码模式
        self.cleanup_pattern = re.compile(
            r'<!-- 新的浮动按钮，使用用户提供的HTML和内联样式 -->.*?</script>',
            re.DOTALL | re.IGNORECASE
        )

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="HTML文件合并工具",
                                font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 目录选择
        ttk.Label(main_frame, text="选择目录:").grid(
            row=1, column=0, sticky=tk.W, pady=5)

        self.dir_var = tk.StringVar()
        dir_entry = ttk.Entry(main_frame, textvariable=self.dir_var, width=50)
        dir_entry.grid(row=1, column=1, sticky=(
            tk.W, tk.E), padx=(5, 5), pady=5)

        browse_btn = ttk.Button(main_frame, text="浏览",
                                command=self.browse_directory)
        browse_btn.grid(row=1, column=2, padx=(5, 0), pady=5)

        # 文件列表框架
        list_frame = ttk.LabelFrame(main_frame, text="找到的HTML文件", padding="5")
        list_frame.grid(row=2, column=0, columnspan=3,
                        sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 文件列表
        self.file_listbox = tk.Listbox(list_frame, height=8)
        self.file_listbox.grid(
            row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        scrollbar = ttk.Scrollbar(
            list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)

        # 刷新按钮
        refresh_btn = ttk.Button(button_frame, text="刷新文件列表",
                                 command=self.refresh_file_list)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 合并按钮
        merge_btn = ttk.Button(button_frame, text="合并HTML文件",
                               command=self.merge_files, style="Accent.TButton")
        merge_btn.pack(side=tk.LEFT)

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("请选择包含HTML文件的目录")
        status_label = ttk.Label(main_frame, textvariable=self.status_var,
                                 relief=tk.SUNKEN, anchor=tk.W)
        status_label.grid(row=4, column=0, columnspan=3,
                          sticky=(tk.W, tk.E), pady=(10, 0))

        # 配置行权重
        main_frame.rowconfigure(2, weight=1)

    def browse_directory(self):
        """浏览并选择目录"""
        directory = filedialog.askdirectory(title="选择包含HTML文件的目录")
        if directory:
            self.dir_var.set(directory)
            self.refresh_file_list()

    def refresh_file_list(self):
        """刷新HTML文件列表"""
        directory = self.dir_var.get()
        if not directory or not os.path.exists(directory):
            self.status_var.set("请选择有效的目录")
            return

        # 清空列表
        self.file_listbox.delete(0, tk.END)

        # 查找HTML文件
        html_files = glob.glob(os.path.join(directory, "*.html"))
        html_files.extend(glob.glob(os.path.join(directory, "*.htm")))

        if not html_files:
            self.status_var.set(f"在目录 {directory} 中未找到HTML文件")
            return

        # 排序并添加到列表
        html_files.sort()
        for file_path in html_files:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, filename)

        self.status_var.set(f"找到 {len(html_files)} 个HTML文件")

    def clean_html_content(self, content):
        """清除HTML内容中的特定代码"""
        # 移除浮动按钮相关代码
        cleaned_content = self.cleanup_pattern.sub('', content)
        return cleaned_content

    def extract_body_content(self, html_content):
        """提取HTML文件的body内容"""
        # 使用正则表达式提取body标签内的内容
        body_match = re.search(
            r'<body[^>]*>(.*?)</body>', html_content, re.DOTALL | re.IGNORECASE)
        if body_match:
            body_content = body_match.group(1).strip()
            # 修正绝对定位问题
            body_content = self.fix_positioning_issues(body_content)
            return body_content
        return ""

    def fix_positioning_issues(self, content):
        """修正内容中的定位问题，防止重叠"""
        # 移除或修正 position: absolute 样式
        # 将 position: absolute 改为 position: relative
        content = re.sub(
            r'style="([^"]*?)position:\s*absolute([^"]*?)"',
            r'style="\1position: relative\2"',
            content,
            flags=re.IGNORECASE
        )

        # 移除可能导致重叠的样式属性
        content = re.sub(
            r'style="([^"]*?)(?:top:\s*[^;]+;?|left:\s*[^;]+;?|right:\s*[^;]+;?|bottom:\s*[^;]+;?)([^"]*?)"',
            r'style="\1\2"',
            content,
            flags=re.IGNORECASE
        )

        # 清理空的style属性
        content = re.sub(r'style=""', '', content)
        content = re.sub(r'style="\s*"', '', content)

        return content

    def extract_head_content(self, html_content):
        """提取HTML文件的head内容"""
        head_match = re.search(
            r'<head[^>]*>(.*?)</head>', html_content, re.DOTALL | re.IGNORECASE)
        if head_match:
            return head_match.group(1).strip()
        return ""

    def merge_files(self):
        """合并HTML文件"""
        directory = self.dir_var.get()
        if not directory or not os.path.exists(directory):
            messagebox.showerror("错误", "请选择有效的目录")
            return

        # 获取所有HTML文件
        html_files = glob.glob(os.path.join(directory, "*.html"))
        html_files.extend(glob.glob(os.path.join(directory, "*.htm")))

        if not html_files:
            messagebox.showwarning("警告", "在选择的目录中未找到HTML文件")
            return

        # 过滤掉已经合并的文件
        html_files = [f for f in html_files if not os.path.basename(
            f).startswith('merged_html_')]

        if not html_files:
            messagebox.showwarning("警告", "在选择的目录中未找到需要合并的HTML文件（排除已合并的文件）")
            return

        try:
            self.status_var.set("正在合并文件...")
            self.root.update()

            # 排序文件列表
            html_files.sort()

            # 存储所有body内容
            all_body_contents = []
            # 存储第一个文件的head内容作为基础
            base_head_content = ""
            base_title = "合并的HTML文件"

            for i, file_path in enumerate(html_files):
                filename = os.path.basename(file_path)
                self.status_var.set(
                    f"正在处理: {filename} ({i+1}/{len(html_files)})")
                self.root.update()

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    # 尝试其他编码
                    try:
                        with open(file_path, 'r', encoding='gbk') as f:
                            content = f.read()
                    except UnicodeDecodeError:
                        with open(file_path, 'r', encoding='latin-1') as f:
                            content = f.read()

                # 清理内容
                cleaned_content = self.clean_html_content(content)

                # 提取body内容
                body_content = self.extract_body_content(cleaned_content)
                if body_content:
                    # 只添加HTML注释作为分隔，不添加可见的分隔符
                    separator = f'\n<!-- ========== 来源文件: {filename} ========== -->\n'
                    all_body_contents.append(separator + body_content)

                # 使用第一个文件的head作为基础
                if i == 0:
                    base_head_content = self.extract_head_content(
                        cleaned_content)
                    # 提取标题
                    title_match = re.search(
                        r'<title[^>]*>(.*?)</title>', base_head_content, re.IGNORECASE)
                    if title_match:
                        base_title = f"合并文件 - {title_match.group(1)}"

            if not all_body_contents:
                messagebox.showwarning("警告", "没有找到有效的HTML内容进行合并")
                return

            # 生成合并后的HTML
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            merged_filename = f"merged_html_{timestamp}.html"
            merged_filepath = os.path.join(directory, merged_filename)

            # 构建完整的HTML文档
            merged_html = self.build_merged_html(
                base_head_content, base_title, all_body_contents)

            # 保存合并后的文件
            with open(merged_filepath, 'w', encoding='utf-8') as f:
                f.write(merged_html)

            self.status_var.set(f"合并完成！文件保存为: {merged_filename}")
            messagebox.showinfo(
                "成功", f"HTML文件合并完成！\n\n合并了 {len(html_files)} 个文件\n保存为: {merged_filename}\n\n文件路径: {merged_filepath}")

            # 刷新文件列表以显示新文件
            self.refresh_file_list()

        except Exception as e:
            error_msg = f"合并过程中发生错误: {str(e)}"
            self.status_var.set("合并失败")
            messagebox.showerror("错误", error_msg)
            import traceback
            print(traceback.format_exc())

    def build_merged_html(self, base_head_content, title, body_contents):
        """构建合并后的HTML文档"""
        # 清理head内容，移除重复的meta标签
        cleaned_head = self.clean_head_content(base_head_content)

        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    {cleaned_head}
    <style>
        /* 合并文件的额外样式 */
        body {{
            padding-bottom: 20px !important;
        }}
        /* 确保浮动按钮不显示 */
        #plugin-trigger-button {{
            display: none !important;
        }}
    </style>
</head>
<body>
{''.join(body_contents)}
</body>
</html>"""
        return html_template

    def clean_head_content(self, head_content):
        """清理head内容，移除重复项"""
        if not head_content:
            return ""

        # 移除重复的meta charset
        head_content = re.sub(
            r'<meta\s+charset="UTF-8"[^>]*>', '', head_content, flags=re.IGNORECASE)
        # 移除重复的viewport meta
        head_content = re.sub(
            r'<meta\s+name="viewport"[^>]*>', '', head_content, flags=re.IGNORECASE)
        # 移除title标签
        head_content = re.sub(
            r'<title[^>]*>.*?</title>', '', head_content, flags=re.IGNORECASE | re.DOTALL)

        return head_content.strip()

    def merge_files_programmatically(self, directory):
        """程序化合并文件，不使用GUI"""
        if not directory or not os.path.exists(directory):
            return None

        # 获取所有HTML文件
        html_files = glob.glob(os.path.join(directory, "*.html"))
        html_files.extend(glob.glob(os.path.join(directory, "*.htm")))

        if not html_files:
            return None

        # 过滤掉已经合并的文件
        html_files = [f for f in html_files if not os.path.basename(
            f).startswith('merged_html_')]

        if not html_files:
            return None

        try:
            # 排序文件列表
            html_files.sort()

            # 存储所有body内容
            all_body_contents = []
            # 存储第一个文件的head内容作为基础
            base_head_content = ""
            base_title = "合并的HTML文件"

            for i, file_path in enumerate(html_files):
                filename = os.path.basename(file_path)
                print(f"正在处理: {filename} ({i+1}/{len(html_files)})")

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    # 尝试其他编码
                    try:
                        with open(file_path, 'r', encoding='gbk') as f:
                            content = f.read()
                    except UnicodeDecodeError:
                        with open(file_path, 'r', encoding='latin-1') as f:
                            content = f.read()

                # 清理内容
                cleaned_content = self.clean_html_content(content)

                # 提取body内容
                body_content = self.extract_body_content(cleaned_content)
                if body_content:
                    # 只添加HTML注释作为分隔，不添加可见的分隔符
                    separator = f'\n<!-- ========== 来源文件: {filename} ========== -->\n'
                    all_body_contents.append(separator + body_content)

                # 使用第一个文件的head作为基础
                if i == 0:
                    base_head_content = self.extract_head_content(
                        cleaned_content)
                    # 提取标题
                    title_match = re.search(
                        r'<title[^>]*>(.*?)</title>', base_head_content, re.IGNORECASE)
                    if title_match:
                        base_title = f"合并文件 - {title_match.group(1)}"

            if not all_body_contents:
                return None

            # 生成合并后的HTML
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            merged_filename = f"merged_html_{timestamp}.html"
            merged_filepath = os.path.join(directory, merged_filename)

            # 构建完整的HTML文档
            merged_html = self.build_merged_html(
                base_head_content, base_title, all_body_contents)

            # 保存合并后的文件
            with open(merged_filepath, 'w', encoding='utf-8') as f:
                f.write(merged_html)

            print(f"合并完成！文件保存为: {merged_filename}")
            return merged_filepath

        except Exception as e:
            print(f"合并过程中发生错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None

    def run(self):
        """运行应用程序"""
        # 设置默认目录为当前目录
        current_dir = os.getcwd()
        self.dir_var.set(current_dir)
        self.refresh_file_list()

        # 启动GUI
        self.root.mainloop()


def main():
    """主函数"""
    app = HTMLMerger()
    app.run()


if __name__ == "__main__":
    main()
