#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HTML文件合并工具
直接运行即可合并当前目录下的HTML文件
"""

import os
import re
import glob
from datetime import datetime


def clean_html_content(content):
    """清除HTML内容中的浮动按钮代码"""
    # 要清除的代码模式
    cleanup_pattern = re.compile(
        r'<!-- 新的浮动按钮，使用用户提供的HTML和内联样式 -->.*?</script>',
        re.DOTALL | re.IGNORECASE
    )
    
    # 移除浮动按钮相关代码
    cleaned_content = cleanup_pattern.sub('', content)
    return cleaned_content


def extract_body_content(html_content):
    """提取HTML文件的body内容"""
    body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, re.DOTALL | re.IGNORECASE)
    if body_match:
        return body_match.group(1).strip()
    return ""


def extract_head_content(html_content):
    """提取HTML文件的head内容"""
    head_match = re.search(r'<head[^>]*>(.*?)</head>', html_content, re.DOTALL | re.IGNORECASE)
    if head_match:
        return head_match.group(1).strip()
    return ""


def clean_head_content(head_content):
    """清理head内容，移除重复项"""
    if not head_content:
        return ""
    
    # 移除重复的meta charset
    head_content = re.sub(r'<meta\s+charset="UTF-8"[^>]*>', '', head_content, flags=re.IGNORECASE)
    # 移除重复的viewport meta
    head_content = re.sub(r'<meta\s+name="viewport"[^>]*>', '', head_content, flags=re.IGNORECASE)
    # 移除title标签
    head_content = re.sub(r'<title[^>]*>.*?</title>', '', head_content, flags=re.IGNORECASE | re.DOTALL)
    
    return head_content.strip()


def build_merged_html(base_head_content, title, body_contents):
    """构建合并后的HTML文档"""
    # 清理head内容，移除重复的meta标签
    cleaned_head = clean_head_content(base_head_content)
    
    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    {cleaned_head}
    <style>
        /* 合并文件的额外样式 */
        body {{
            padding-bottom: 20px !important;
        }}
        /* 确保浮动按钮不显示 */
        #plugin-trigger-button {{
            display: none !important;
        }}
    </style>
</head>
<body>
{''.join(body_contents)}
</body>
</html>"""
    return html_template


def merge_html_files(directory=None):
    """合并指定目录下的HTML文件"""
    if directory is None:
        directory = os.getcwd()
    
    print(f"正在处理目录: {directory}")
    
    # 获取所有HTML文件
    html_files = glob.glob(os.path.join(directory, "*.html"))
    html_files.extend(glob.glob(os.path.join(directory, "*.htm")))
    
    if not html_files:
        print("在选择的目录中未找到HTML文件")
        return None
    
    # 过滤掉已经合并的文件
    html_files = [f for f in html_files if not os.path.basename(f).startswith('merged_html_')]
    
    if not html_files:
        print("在选择的目录中未找到需要合并的HTML文件（排除已合并的文件）")
        return None
    
    # 排序文件列表
    html_files.sort()
    print(f"找到 {len(html_files)} 个HTML文件:")
    for file_path in html_files:
        print(f"  - {os.path.basename(file_path)}")
    
    # 存储所有body内容
    all_body_contents = []
    # 存储第一个文件的head内容作为基础
    base_head_content = ""
    base_title = "合并的HTML文件"
    
    for i, file_path in enumerate(html_files):
        filename = os.path.basename(file_path)
        print(f"正在处理: {filename} ({i+1}/{len(html_files)})")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        # 清理内容
        cleaned_content = clean_html_content(content)
        
        # 提取body内容
        body_content = extract_body_content(cleaned_content)
        if body_content:
            # 只添加HTML注释作为分隔，不添加可见的分隔符
            separator = f'\n<!-- ========== 来源文件: {filename} ========== -->\n'
            all_body_contents.append(separator + body_content)
        
        # 使用第一个文件的head作为基础
        if i == 0:
            base_head_content = extract_head_content(cleaned_content)
            # 提取标题
            title_match = re.search(r'<title[^>]*>(.*?)</title>', base_head_content, re.IGNORECASE)
            if title_match:
                base_title = f"合并文件 - {title_match.group(1)}"
    
    if not all_body_contents:
        print("没有找到有效的HTML内容进行合并")
        return None
    
    # 生成合并后的HTML
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    merged_filename = f"merged_html_{timestamp}.html"
    merged_filepath = os.path.join(directory, merged_filename)
    
    # 构建完整的HTML文档
    merged_html = build_merged_html(base_head_content, base_title, all_body_contents)
    
    # 保存合并后的文件
    with open(merged_filepath, 'w', encoding='utf-8') as f:
        f.write(merged_html)
    
    print(f"合并完成！文件保存为: {merged_filename}")
    print(f"完整路径: {merged_filepath}")
    return merged_filepath


if __name__ == "__main__":
    print("HTML文件合并工具")
    print("=" * 50)
    
    # 可以指定目录，或者使用当前目录
    target_directory = input("请输入目录路径（直接回车使用当前目录）: ").strip()
    if not target_directory:
        target_directory = os.getcwd()
    
    if not os.path.exists(target_directory):
        print(f"错误：目录 {target_directory} 不存在")
    else:
        try:
            result = merge_html_files(target_directory)
            if result:
                print(f"\n✅ 成功创建合并文件: {result}")
            else:
                print("\n❌ 合并失败")
        except Exception as e:
            print(f"\n❌ 合并过程中发生错误: {str(e)}")
    
    input("\n按回车键退出...")
